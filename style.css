body{
	font-family: '<PERSON><PERSON><PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
	font-size: 14pt;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	margin: 0;
	padding: 20px;
	min-height: 100vh;
}

th{
	text-align: left;
	font-weight: 600;
	color: #2c3e50;
}

img{
	width: 100%;
	margin-bottom: 10px;
	border-radius: 8px;
	box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.kotak{
	background: white;
	border-radius: 12px;
	box-shadow: 0 8px 32px rgba(0,0,0,0.1);
	width: 60%;
	margin: 20px auto;
	padding: 30px;
	border: none;
}

.judul{
	text-align: center;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	margin: -30px -30px 25px -30px;
	padding: 25px;
	border-radius: 12px 12px 0 0;
}

.judul h1{
	margin: 0;
	font-size: 28px;
	letter-spacing: 2px;
}

.blok{
	overflow: hidden;
	margin-bottom: 25px;
	padding: 20px;
	background: #f8f9fa;
	border-radius: 8px;
	border-left: 4px solid #667eea;
}

.blok h2{
	color: #2c3e50;
	margin-top: 0;
	font-size: 20px;
	border-bottom: 2px solid #ecf0f1;
	padding-bottom: 10px;
}

.kiri{
	width: 20%;
	float: left;
}

.kanan{
	width: 70%;
	padding: 0px 20px;
	float: left;
}

table{
	width: 100%;
	border-collapse: collapse;
}

table tr{
	border-bottom: 1px solid #ecf0f1;
}

table th, table td{
	padding: 8px 5px;
	vertical-align: top;
}

ul{
	padding-left: 20px;
}

ul li{
	margin-bottom: 8px;
	line-height: 1.5;
}

a{
	color: #667eea;
	text-decoration: none;
}

a:hover{
	text-decoration: underline;
}

@media only screen and (max-width: 768px) {
	body{
		padding: 10px;
	}

	.kotak{
		width: 100%;
		padding: 20px;
		margin: 10px auto;
	}

	.judul{
		margin: -20px -20px 20px -20px;
		padding: 20px;
	}

	.judul h1{
		font-size: 24px;
	}

	.kiri{
		width: 100%;
		margin-bottom: 15px;
	}

	.kanan{
		width: 100%;
		padding: 0;
	}

	.blok{
		padding: 15px;
	}
}