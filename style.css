body{
	font-family: '<PERSON><PERSON><PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
	font-size: 14pt;
	background: linear-gradient(135deg, #ffeef8 0%, #e8f4fd 50%, #f0f8e8 100%);
	background-size: 400% 400%;
	margin: 0;
	padding: 0;
	min-height: 100vh;
	animation: backgroundShift 15s ease-in-out infinite;
}

@keyframes backgroundShift {
	0% { background-position: 0% 50%; }
	50% { background-position: 100% 50%; }
	100% { background-position: 0% 50%; }
}

.scrolling-text {
	background: linear-gradient(90deg, #ffd6e8, #e8f4fd, #d6f5d6);
	padding: 15px 0;
	overflow: hidden;
	white-space: nowrap;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	margin-bottom: 20px;
}

.scroll-content {
	display: inline-block;
	animation: scroll 20s linear infinite;
	font-size: 16px;
	font-weight: 500;
	color: #6b5b95;
	letter-spacing: 1px;
}

@keyframes scroll {
	0% { transform: translateX(100%); }
	100% { transform: translateX(-100%); }
}

th{
	text-align: left;
	font-weight: 600;
	color: #6b5b95;
}

.photo-frame {
	position: relative;
	display: block;
	width: 100%;
	padding: 0;
	margin-bottom: 10px;
	overflow: hidden;
	border-radius: 20px;
}

.photo-frame::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: conic-gradient(
		from 0deg,
		#ff9a9e 0deg,
		#fecfef 60deg,
		#fecfef 120deg,
		#a8e6cf 180deg,
		#88d8c0 240deg,
		#ffd3a5 300deg,
		#ff9a9e 360deg
	);
	animation: borderSpin 4s linear infinite;
	z-index: 1;
}

.photo-frame::after {
	content: '';
	position: absolute;
	top: 3px;
	left: 3px;
	right: 3px;
	bottom: 3px;
	background: white;
	border-radius: 17px;
	z-index: 2;
}

@keyframes borderSpin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.profile-photo {
	position: relative;
	width: 100%;
	height: auto;
	border-radius: 15px;
	display: block;
	z-index: 3;
	margin: 6px;
	transition: all 0.4s ease;
	filter: brightness(1.05) contrast(1.1);
}

.photo-frame:hover .profile-photo {
	transform: scale(1.03);
	filter: brightness(1.1) contrast(1.15) saturate(1.1);
}

.photo-frame:hover::before {
	animation-duration: 2s;
}

.kotak{
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20px;
	box-shadow: 0 15px 40px rgba(0,0,0,0.1);
	width: 60%;
	margin: 20px auto;
	padding: 30px;
	border: 1px solid rgba(255, 255, 255, 0.5);
	backdrop-filter: blur(15px);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.kotak::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
	transition: left 0.5s ease;
}

.kotak:hover {
	transform: translateY(-3px);
	box-shadow: 0 25px 60px rgba(0,0,0,0.15);
}

.kotak:hover::before {
	left: 100%;
}

.judul{
	text-align: center;
	background: linear-gradient(135deg, #a8e6cf 0%, #ffd3a5 50%, #ffd6e8 100%);
	background-size: 200% 200%;
	color: #6b5b95;
	margin: -30px -30px 25px -30px;
	padding: 25px;
	border-radius: 20px 20px 0 0;
	animation: titleShimmer 6s ease-in-out infinite;
	position: relative;
	z-index: 10;
}

@keyframes titleShimmer {
	0% { background-position: 0% 50%; }
	50% { background-position: 100% 50%; }
	100% { background-position: 0% 50%; }
}

.judul h1{
	margin: 0;
	font-size: 28px;
	letter-spacing: 2px;
	text-shadow: 2px 2px 4px rgba(255,255,255,0.5);
	position: relative;
	z-index: 11;
}

.blok{
	overflow: hidden;
	margin-bottom: 25px;
	padding: 20px;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 249, 250, 0.9) 100%);
	border-radius: 15px;
	border-left: 4px solid #a8e6cf;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.blok::after {
	content: "";
	display: table;
	clear: both;
}

.blok:hover {
	transform: translateX(5px);
	box-shadow: 0 8px 25px rgba(0,0,0,0.1);
	border-left: 6px solid #ffd3a5;
}

.blok h2{
	color: #6b5b95;
	margin-top: 0;
	font-size: 20px;
	border-bottom: 2px solid rgba(168, 230, 207, 0.5);
	padding-bottom: 10px;
	transition: color 0.3s ease;
}

.blok:hover h2 {
	color: #8e44ad;
}

.kiri{
	width: 20%;
	float: left;
}

.kanan{
	width: 70%;
	padding: 0px 20px;
	float: left;
}

table{
	width: 100%;
	border-collapse: collapse;
}

table tr{
	border-bottom: 1px solid rgba(168, 230, 207, 0.3);
	transition: background-color 0.3s ease;
}

table tr:hover {
	background-color: rgba(255, 214, 232, 0.2);
}

table th, table td{
	padding: 8px 5px;
	vertical-align: top;
}

ul{
	padding-left: 20px;
}

ul li{
	margin-bottom: 8px;
	line-height: 1.5;
	transition: transform 0.2s ease;
}

ul li:hover {
	transform: translateX(5px);
	color: #8e44ad;
}

a{
	color: #6b5b95;
	text-decoration: none;
	transition: all 0.3s ease;
	padding: 3px 8px;
	border-radius: 8px;
	font-weight: 500;
	border: 1px solid transparent;
}

a:hover{
	background: linear-gradient(45deg, #ffd6e8, #e8f4fd);
	color: #8e44ad;
	transform: translateY(-1px);
	border: 1px solid rgba(168, 230, 207, 0.5);
	box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

@media only screen and (max-width: 768px) {
	body{
		padding: 10px;
	}

	.scrolling-text {
		padding: 10px 0;
		margin-bottom: 15px;
	}

	.scroll-content {
		font-size: 14px;
	}

	.kotak{
		width: calc(100% - 20px);
		padding: 20px;
		margin: 10px auto;
	}

	.judul{
		margin: -20px -20px 20px -20px;
		padding: 20px;
	}

	.judul h1{
		font-size: 22px;
		letter-spacing: 1px;
	}

	.kiri{
		width: 100%;
		margin-bottom: 15px;
		float: none;
	}

	.kanan{
		width: 100%;
		padding: 0;
		float: none;
	}

	.blok{
		padding: 15px;
		margin-bottom: 20px;
	}

	.photo-frame {
		max-width: 200px;
		margin: 0 auto 15px auto;
	}

	.blok:after {
		content: "";
		display: table;
		clear: both;
	}
}