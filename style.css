body{
	font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
	font-size: 14pt;
	background: linear-gradient(135deg, #ffeef8 0%, #e8f4fd 50%, #f0f8e8 100%);
	margin: 0;
	padding: 0;
	min-height: 100vh;
	animation: backgroundShift 10s ease-in-out infinite alternate;
}

@keyframes backgroundShift {
	0% { background: linear-gradient(135deg, #ffeef8 0%, #e8f4fd 50%, #f0f8e8 100%); }
	100% { background: linear-gradient(135deg, #f0f8e8 0%, #ffeef8 50%, #e8f4fd 100%); }
}

.scrolling-text {
	background: linear-gradient(90deg, #ffd6e8, #e8f4fd, #d6f5d6);
	padding: 15px 0;
	overflow: hidden;
	white-space: nowrap;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	margin-bottom: 20px;
}

.scroll-content {
	display: inline-block;
	animation: scroll 20s linear infinite;
	font-size: 16px;
	font-weight: 500;
	color: #6b5b95;
	letter-spacing: 1px;
}

@keyframes scroll {
	0% { transform: translateX(100%); }
	100% { transform: translateX(-100%); }
}

th{
	text-align: left;
	font-weight: 600;
	color: #6b5b95;
}

.photo-frame {
	position: relative;
	display: inline-block;
	padding: 8px;
	background: linear-gradient(45deg, #ffd6e8, #e8f4fd, #d6f5d6, #fff2cc);
	border-radius: 20px;
	animation: frameGlow 3s ease-in-out infinite alternate;
	box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.photo-frame::before {
	content: '';
	position: absolute;
	top: -3px;
	left: -3px;
	right: -3px;
	bottom: -3px;
	background: linear-gradient(45deg, #ff9a9e, #a8e6cf, #ffd3a5, #c3cfe2);
	border-radius: 23px;
	z-index: -1;
	animation: borderRotate 4s linear infinite;
}

@keyframes frameGlow {
	0% {
		box-shadow: 0 8px 25px rgba(255, 182, 193, 0.3);
		transform: scale(1);
	}
	100% {
		box-shadow: 0 12px 35px rgba(173, 216, 230, 0.4);
		transform: scale(1.02);
	}
}

@keyframes borderRotate {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.profile-photo {
	width: 100%;
	margin-bottom: 10px;
	border-radius: 15px;
	display: block;
	transition: transform 0.3s ease;
}

.profile-photo:hover {
	transform: scale(1.05);
}

.kotak{
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20px;
	box-shadow: 0 15px 40px rgba(0,0,0,0.1);
	width: 60%;
	margin: 20px auto;
	padding: 30px;
	border: 2px solid rgba(255, 255, 255, 0.3);
	backdrop-filter: blur(10px);
	transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.kotak:hover {
	transform: translateY(-5px);
	box-shadow: 0 20px 50px rgba(0,0,0,0.15);
}

.judul{
	text-align: center;
	background: linear-gradient(135deg, #a8e6cf 0%, #ffd3a5 50%, #ffd6e8 100%);
	color: #6b5b95;
	margin: -30px -30px 25px -30px;
	padding: 25px;
	border-radius: 20px 20px 0 0;
	animation: titleShimmer 4s ease-in-out infinite alternate;
}

@keyframes titleShimmer {
	0% { background: linear-gradient(135deg, #a8e6cf 0%, #ffd3a5 50%, #ffd6e8 100%); }
	100% { background: linear-gradient(135deg, #ffd6e8 0%, #e8f4fd 50%, #a8e6cf 100%); }
}

.judul h1{
	margin: 0;
	font-size: 28px;
	letter-spacing: 2px;
	text-shadow: 2px 2px 4px rgba(255,255,255,0.5);
}

.blok{
	overflow: hidden;
	margin-bottom: 25px;
	padding: 20px;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 249, 250, 0.9) 100%);
	border-radius: 15px;
	border-left: 4px solid #a8e6cf;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.blok:hover {
	transform: translateX(5px);
	box-shadow: 0 8px 25px rgba(0,0,0,0.1);
	border-left: 6px solid #ffd3a5;
}

.blok h2{
	color: #6b5b95;
	margin-top: 0;
	font-size: 20px;
	border-bottom: 2px solid rgba(168, 230, 207, 0.5);
	padding-bottom: 10px;
	transition: color 0.3s ease;
}

.blok:hover h2 {
	color: #8e44ad;
}

.kiri{
	width: 20%;
	float: left;
}

.kanan{
	width: 70%;
	padding: 0px 20px;
	float: left;
}

table{
	width: 100%;
	border-collapse: collapse;
}

table tr{
	border-bottom: 1px solid rgba(168, 230, 207, 0.3);
	transition: background-color 0.3s ease;
}

table tr:hover {
	background-color: rgba(255, 214, 232, 0.2);
}

table th, table td{
	padding: 8px 5px;
	vertical-align: top;
}

ul{
	padding-left: 20px;
}

ul li{
	margin-bottom: 8px;
	line-height: 1.5;
	transition: transform 0.2s ease;
}

ul li:hover {
	transform: translateX(5px);
	color: #8e44ad;
}

a{
	color: #a8e6cf;
	text-decoration: none;
	transition: all 0.3s ease;
	padding: 2px 6px;
	border-radius: 8px;
}

a:hover{
	background: linear-gradient(45deg, #ffd6e8, #e8f4fd);
	color: #6b5b95;
	transform: scale(1.05);
}

@media only screen and (max-width: 768px) {
	body{
		padding: 10px;
	}

	.scrolling-text {
		padding: 10px 0;
	}

	.scroll-content {
		font-size: 14px;
	}

	.kotak{
		width: 100%;
		padding: 20px;
		margin: 10px auto;
	}

	.judul{
		margin: -20px -20px 20px -20px;
		padding: 20px;
	}

	.judul h1{
		font-size: 24px;
	}

	.kiri{
		width: 100%;
		margin-bottom: 15px;
	}

	.kanan{
		width: 100%;
		padding: 0;
	}

	.blok{
		padding: 15px;
	}

	.photo-frame {
		padding: 6px;
	}
}