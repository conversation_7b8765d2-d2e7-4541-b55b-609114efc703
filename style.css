:root {
	--primary-color: #8B4513;
	--secondary-color: #D2691E;
	--accent-color: #F4A460;
	--warm-beige: #F5F5DC;
	--soft-cream: #FFF8DC;
	--earth-brown: #A0522D;
	--light-tan: #DEB887;
	--deep-orange: #FF8C00;
	--text-dark: #2F1B14;
	--text-light: #6B4423;
	--card-bg: rgba(255, 248, 220, 0.95);
	--shadow-light: rgba(139, 69, 19, 0.1);
	--shadow-medium: rgba(139, 69, 19, 0.2);
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: 'Poppins', sans-serif;
	line-height: 1.6;
	color: var(--text-dark);
	background: linear-gradient(135deg, var(--warm-beige) 0%, var(--soft-cream) 50%, var(--light-tan) 100%);
	background-size: 400% 400%;
	animation: backgroundFlow 20s ease-in-out infinite;
	overflow-x: hidden;
}

@keyframes backgroundFlow {
	0%, 100% { background-position: 0% 50%; }
	50% { background-position: 100% 50%; }
}

.hero-section {
	position: relative;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	overflow: hidden;
}

.floating-elements {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 1;
}

.float-element {
	position: absolute;
	width: 60px;
	height: 60px;
	background: linear-gradient(45deg, var(--accent-color), var(--secondary-color));
	border-radius: 50%;
	opacity: 0.1;
	animation: float 15s infinite ease-in-out;
}

.float-element:nth-child(1) {
	top: 20%;
	left: 10%;
	animation-delay: 0s;
}

.float-element:nth-child(2) {
	top: 60%;
	right: 15%;
	animation-delay: 5s;
	width: 80px;
	height: 80px;
}

.float-element:nth-child(3) {
	bottom: 20%;
	left: 20%;
	animation-delay: 10s;
	width: 40px;
	height: 40px;
}

@keyframes float {
	0%, 100% { transform: translateY(0px) rotate(0deg); }
	33% { transform: translateY(-30px) rotate(120deg); }
	66% { transform: translateY(20px) rotate(240deg); }
}

.scrolling-text {
	position: relative;
	background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
	padding: 12px 0;
	overflow: hidden;
	white-space: nowrap;
	box-shadow: 0 4px 20px var(--shadow-light);
	margin-bottom: 40px;
	z-index: 10;
	width: 100%;
}

.scroll-content {
	display: inline-block;
	animation: scroll 25s linear infinite;
	font-size: 16px;
	font-weight: 500;
	color: white;
	letter-spacing: 1px;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

@keyframes scroll {
	0% { transform: translateX(100%); }
	100% { transform: translateX(-100%); }
}

.hero-content {
	position: relative;
	z-index: 10;
	text-align: center;
	max-width: 800px;
	padding: 0 20px;
}

.profile-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 30px;
}

.photo-container {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.photo-frame {
	position: relative;
	width: 200px;
	height: 200px;
	border-radius: 50%;
	overflow: hidden;
	background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
	padding: 4px;
	box-shadow: 0 10px 30px var(--shadow-medium);
}

.photo-frame::before {
	content: '';
	position: absolute;
	inset: -15px;
	background: conic-gradient(
		from 0deg,
		#ff0000 0deg,
		#ff7f00 60deg,
		#ffff00 120deg,
		#00ff00 180deg,
		#0000ff 240deg,
		#8f00ff 300deg,
		#ff0000 360deg
	);
	border-radius: 50%;
	z-index: -1;
	animation: rgbGlow 3s ease-in-out infinite;
	opacity: 0.6;
	filter: blur(12px);
}

.photo-frame::after {
	content: '';
	position: absolute;
	inset: -25px;
	background: radial-gradient(
		circle at 30% 30%,
		rgba(255, 0, 0, 0.6) 0%,
		rgba(0, 255, 0, 0.4) 30%,
		rgba(0, 0, 255, 0.6) 60%,
		transparent 100%
	);
	border-radius: 50%;
	z-index: -2;
	animation: eyeShine 3s ease-in-out infinite;
	opacity: 0.7;
}

@keyframes rgbGlow {
	0%, 100% {
		transform: scale(1) rotate(0deg);
		filter: blur(8px) brightness(1);
	}
	25% {
		transform: scale(1.05) rotate(90deg);
		filter: blur(6px) brightness(1.2);
	}
	50% {
		transform: scale(1.1) rotate(180deg);
		filter: blur(4px) brightness(1.4);
	}
	75% {
		transform: scale(1.05) rotate(270deg);
		filter: blur(6px) brightness(1.2);
	}
}

@keyframes eyeShine {
	0%, 100% {
		opacity: 0.3;
		transform: scale(1) rotate(0deg);
	}
	25% {
		opacity: 0.6;
		transform: scale(1.1) rotate(45deg);
	}
	50% {
		opacity: 0.9;
		transform: scale(1.2) rotate(90deg);
	}
	75% {
		opacity: 0.6;
		transform: scale(1.1) rotate(135deg);
	}
}

.profile-photo {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 50%;
	transition: all 0.4s ease;
	filter: brightness(1.05) contrast(1.1);
}

.photo-frame:hover .profile-photo {
	transform: scale(1.05);
	filter: brightness(1.1) contrast(1.15) saturate(1.1);
}

.photo-frame:hover::before {
	animation-duration: 1s;
	opacity: 1;
	filter: blur(4px) brightness(1.8);
}

.photo-frame:hover::after {
	animation-duration: 1.5s;
	opacity: 1;
}

.photo-frame:hover .eye-effect::before {
	animation-duration: 0.8s;
	width: 20px;
	height: 20px;
	filter: blur(0px);
	box-shadow:
		0 0 40px rgba(255, 0, 0, 1),
		0 0 80px rgba(0, 255, 255, 0.8),
		0 0 120px rgba(255, 255, 0, 0.6),
		inset 0 0 20px rgba(255, 255, 255, 0.8);
}

.photo-frame:hover .eye-effect::after {
	animation-duration: 0.8s;
	width: 20px;
	height: 20px;
	filter: blur(0px);
	box-shadow:
		0 0 40px rgba(0, 255, 0, 1),
		0 0 80px rgba(255, 0, 255, 0.8),
		0 0 120px rgba(0, 255, 255, 0.6),
		inset 0 0 20px rgba(255, 255, 255, 0.8);
}

.photo-frame .eye-effect {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 10;
	border-radius: 50%;
	overflow: hidden;
}

.photo-frame .eye-effect::before {
	content: '';
	position: absolute;
	top: 32%;
	left: 38%;
	width: 16px;
	height: 16px;
	background: radial-gradient(circle,
		rgba(255, 255, 255, 1) 0%,
		rgba(255, 0, 0, 0.9) 20%,
		rgba(0, 255, 255, 0.8) 40%,
		rgba(255, 255, 0, 0.7) 60%,
		rgba(255, 0, 255, 0.6) 80%,
		transparent 100%
	);
	border-radius: 50%;
	animation: leftEyeGlow 1.8s ease-in-out infinite;
	box-shadow:
		0 0 20px rgba(255, 0, 0, 0.8),
		0 0 40px rgba(0, 255, 255, 0.6),
		0 0 60px rgba(255, 255, 0, 0.4),
		inset 0 0 15px rgba(255, 255, 255, 0.5);
	filter: blur(1px);
}

.photo-frame .eye-effect::after {
	content: '';
	position: absolute;
	top: 32%;
	right: 38%;
	width: 16px;
	height: 16px;
	background: radial-gradient(circle,
		rgba(255, 255, 255, 1) 0%,
		rgba(0, 255, 0, 0.9) 20%,
		rgba(255, 0, 255, 0.8) 40%,
		rgba(0, 255, 255, 0.7) 60%,
		rgba(255, 100, 0, 0.6) 80%,
		transparent 100%
	);
	border-radius: 50%;
	animation: rightEyeGlow 1.8s ease-in-out infinite 0.4s;
	box-shadow:
		0 0 20px rgba(0, 255, 0, 0.8),
		0 0 40px rgba(255, 0, 255, 0.6),
		0 0 60px rgba(0, 255, 255, 0.4),
		inset 0 0 15px rgba(255, 255, 255, 0.5);
	filter: blur(1px);
}

@keyframes leftEyeGlow {
	0% {
		opacity: 0.3;
		transform: scale(0.6);
		filter: brightness(1) hue-rotate(0deg) blur(2px);
		box-shadow:
			0 0 15px rgba(255, 0, 0, 0.6),
			0 0 30px rgba(255, 0, 0, 0.4);
	}
	25% {
		opacity: 0.8;
		transform: scale(1.2);
		filter: brightness(2) hue-rotate(90deg) blur(0.5px);
		box-shadow:
			0 0 25px rgba(0, 255, 0, 0.8),
			0 0 50px rgba(0, 255, 0, 0.6),
			0 0 75px rgba(0, 255, 0, 0.4);
	}
	50% {
		opacity: 1;
		transform: scale(1.5);
		filter: brightness(3) hue-rotate(180deg) blur(0px);
		box-shadow:
			0 0 30px rgba(0, 0, 255, 1),
			0 0 60px rgba(0, 0, 255, 0.8),
			0 0 90px rgba(0, 0, 255, 0.6);
	}
	75% {
		opacity: 0.9;
		transform: scale(1.3);
		filter: brightness(2.5) hue-rotate(270deg) blur(0.5px);
		box-shadow:
			0 0 25px rgba(255, 255, 0, 0.8),
			0 0 50px rgba(255, 255, 0, 0.6),
			0 0 75px rgba(255, 255, 0, 0.4);
	}
	100% {
		opacity: 0.3;
		transform: scale(0.6);
		filter: brightness(1) hue-rotate(360deg) blur(2px);
		box-shadow:
			0 0 15px rgba(255, 0, 255, 0.6),
			0 0 30px rgba(255, 0, 255, 0.4);
	}
}

@keyframes rightEyeGlow {
	0% {
		opacity: 0.3;
		transform: scale(0.6);
		filter: brightness(1) hue-rotate(180deg) blur(2px);
		box-shadow:
			0 0 15px rgba(0, 255, 0, 0.6),
			0 0 30px rgba(0, 255, 0, 0.4);
	}
	25% {
		opacity: 0.8;
		transform: scale(1.2);
		filter: brightness(2) hue-rotate(270deg) blur(0.5px);
		box-shadow:
			0 0 25px rgba(255, 0, 255, 0.8),
			0 0 50px rgba(255, 0, 255, 0.6),
			0 0 75px rgba(255, 0, 255, 0.4);
	}
	50% {
		opacity: 1;
		transform: scale(1.5);
		filter: brightness(3) hue-rotate(360deg) blur(0px);
		box-shadow:
			0 0 30px rgba(255, 255, 0, 1),
			0 0 60px rgba(255, 255, 0, 0.8),
			0 0 90px rgba(255, 255, 0, 0.6);
	}
	75% {
		opacity: 0.9;
		transform: scale(1.3);
		filter: brightness(2.5) hue-rotate(450deg) blur(0.5px);
		box-shadow:
			0 0 25px rgba(0, 255, 255, 0.8),
			0 0 50px rgba(0, 255, 255, 0.6),
			0 0 75px rgba(0, 255, 255, 0.4);
	}
	100% {
		opacity: 0.3;
		transform: scale(0.6);
		filter: brightness(1) hue-rotate(540deg) blur(2px);
		box-shadow:
			0 0 15px rgba(255, 100, 0, 0.6),
			0 0 30px rgba(255, 100, 0, 0.4);
	}
}

.profile-badge {
	position: absolute;
	bottom: 10px;
	right: 10px;
	background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
	color: white;
	padding: 8px 16px;
	border-radius: 20px;
	font-size: 12px;
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: 1px;
	box-shadow: 0 4px 15px var(--shadow-light);
	animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.05); }
}

.profile-info {
	text-align: center;
}

.name-title {
	font-size: 3rem;
	font-weight: 700;
	color: var(--primary-color);
	margin-bottom: 10px;
	text-shadow: 2px 2px 4px var(--shadow-light);
	animation: titleSlide 1s ease-out;
}

@keyframes titleSlide {
	0% { opacity: 0; transform: translateY(30px); }
	100% { opacity: 1; transform: translateY(0); }
}

.subtitle {
	font-size: 1.3rem;
	color: var(--secondary-color);
	font-weight: 500;
	margin-bottom: 5px;
	animation: titleSlide 1s ease-out 0.2s both;
}

.school {
	font-size: 1.1rem;
	color: var(--text-light);
	font-weight: 400;
	animation: titleSlide 1s ease-out 0.4s both;
}

.main-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 40px 20px;
}

.grid-layout {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
	gap: 25px;
	margin-top: 20px;
}

.card {
	background: var(--card-bg);
	border-radius: 20px;
	padding: 25px;
	box-shadow: 0 8px 30px var(--shadow-light);
	border: 1px solid rgba(139, 69, 19, 0.1);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.card::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
	transition: left 0.6s ease;
}

.card:hover {
	transform: translateY(-8px);
	box-shadow: 0 15px 40px var(--shadow-medium);
}

.card:hover::before {
	left: 100%;
}

.card-header {
	margin-bottom: 20px;
	border-bottom: 2px solid var(--accent-color);
	padding-bottom: 15px;
}

.card-header h2 {
	font-size: 1.4rem;
	color: var(--primary-color);
	font-weight: 600;
	display: flex;
	align-items: center;
	gap: 10px;
}

.icon {
	font-size: 1.2rem;
}

.personal-info {
	grid-column: span 1;
}

.contact-info {
	grid-column: span 1;
}

.education-card {
	grid-column: span 2;
}

.skills-card {
	grid-column: span 1;
}

.activities-card {
	grid-column: span 1;
}

.hobbies-card {
	grid-column: span 1;
}

.info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 15px;
}

.info-item {
	display: flex;
	flex-direction: column;
	gap: 5px;
}

.info-item.full-width {
	grid-column: span 2;
}

.label {
	font-size: 0.9rem;
	color: var(--text-light);
	font-weight: 500;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.value {
	font-size: 1rem;
	color: var(--text-dark);
	font-weight: 600;
}

.contact-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.contact-item {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px 15px;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 12px;
	text-decoration: none;
	color: var(--text-dark);
	transition: all 0.3s ease;
	border: 1px solid transparent;
}

.contact-item:hover {
	background: linear-gradient(45deg, var(--accent-color), var(--light-tan));
	color: white;
	transform: translateX(5px);
	border: 1px solid var(--secondary-color);
	box-shadow: 0 4px 15px var(--shadow-light);
}

.contact-icon {
	font-size: 1.2rem;
	width: 24px;
	text-align: center;
}

.contact-text {
	font-weight: 500;
}

.timeline {
	position: relative;
	padding-left: 30px;
}

.timeline::before {
	content: '';
	position: absolute;
	left: 15px;
	top: 0;
	bottom: 0;
	width: 2px;
	background: linear-gradient(to bottom, var(--primary-color), var(--accent-color));
}

.timeline-item {
	position: relative;
	margin-bottom: 25px;
	padding-left: 25px;
}

.timeline-item::before {
	content: '';
	position: absolute;
	left: -8px;
	top: 5px;
	width: 12px;
	height: 12px;
	background: var(--secondary-color);
	border-radius: 50%;
	border: 3px solid var(--card-bg);
	box-shadow: 0 0 0 3px var(--secondary-color);
}

.timeline-year {
	font-size: 0.9rem;
	color: var(--secondary-color);
	font-weight: 600;
	margin-bottom: 5px;
}

.timeline-content h3 {
	font-size: 1.1rem;
	color: var(--primary-color);
	margin-bottom: 3px;
	font-weight: 600;
}

.timeline-content p {
	color: var(--text-light);
	font-size: 0.95rem;
}

.skills-grid {
	display: flex;
	flex-direction: column;
	gap: 15px;
}

.skill-item {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.skill-name {
	font-weight: 600;
	color: var(--text-dark);
	font-size: 0.95rem;
}

.skill-level {
	background: rgba(139, 69, 19, 0.1);
	border-radius: 10px;
	height: 8px;
	overflow: hidden;
	position: relative;
}

.skill-bar {
	height: 100%;
	background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
	border-radius: 10px;
	transition: width 2s ease-in-out;
	position: relative;
	overflow: hidden;
}

.skill-bar::after {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
	animation: skillShine 2s ease-in-out infinite;
}

@keyframes skillShine {
	0% { left: -100%; }
	100% { left: 100%; }
}

.skill-bar[data-level="85"] { width: 85%; }
.skill-bar[data-level="70"] { width: 70%; }
.skill-bar[data-level="45"] { width: 45%; }
.skill-bar[data-level="35"] { width: 35%; }
.skill-bar[data-level="25"] { width: 25%; }

.activity-item {
	display: flex;
	align-items: center;
	gap: 15px;
	padding: 15px;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 15px;
	transition: all 0.3s ease;
}

.activity-item:hover {
	background: rgba(255, 255, 255, 0.9);
	transform: translateY(-3px);
	box-shadow: 0 8px 25px var(--shadow-light);
}

.activity-icon {
	font-size: 2rem;
	width: 50px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
	border-radius: 50%;
	color: white;
}

.activity-content h3 {
	color: var(--primary-color);
	font-size: 1.1rem;
	margin-bottom: 5px;
	font-weight: 600;
}

.activity-content p {
	color: var(--text-light);
	font-size: 0.9rem;
	margin-bottom: 8px;
}

.activity-status {
	background: linear-gradient(45deg, var(--accent-color), var(--secondary-color));
	color: white;
	padding: 4px 12px;
	border-radius: 15px;
	font-size: 0.8rem;
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.hobbies-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.hobby-item {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px 15px;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 12px;
	transition: all 0.3s ease;
}

.hobby-item:hover {
	background: linear-gradient(45deg, var(--accent-color), var(--light-tan));
	color: white;
	transform: translateX(5px);
	box-shadow: 0 4px 15px var(--shadow-light);
}

.hobby-icon {
	font-size: 1.3rem;
	width: 30px;
	text-align: center;
}

.hobby-text {
	font-weight: 500;
}

.footer {
	background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
	color: white;
	text-align: center;
	padding: 30px 20px;
	margin-top: 50px;
}

.footer p {
	margin: 0;
	font-size: 1rem;
	font-weight: 500;
}

@media only screen and (max-width: 768px) {
	.hero-section {
		min-height: 80vh;
		padding: 20px 0;
	}

	.profile-section {
		gap: 20px;
	}

	.photo-frame {
		width: 150px;
		height: 150px;
	}

	.name-title {
		font-size: 2rem;
	}

	.subtitle {
		font-size: 1.1rem;
	}

	.school {
		font-size: 1rem;
	}

	.grid-layout {
		grid-template-columns: 1fr;
		gap: 20px;
	}

	.card {
		padding: 20px;
	}

	.education-card,
	.skills-card,
	.activities-card,
	.hobbies-card,
	.personal-info,
	.contact-info {
		grid-column: span 1;
	}

	.info-grid {
		grid-template-columns: 1fr;
		gap: 12px;
	}

	.info-item.full-width {
		grid-column: span 1;
	}

	.main-container {
		padding: 20px 15px;
	}

	.scrolling-text {
		padding: 10px 0;
		margin-bottom: 20px;
	}

	.scroll-content {
		font-size: 14px;
	}
}